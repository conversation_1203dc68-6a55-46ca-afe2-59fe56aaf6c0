#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import logging
from datetime import datetime
import time
import os

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealDiscountScraper:
    """كلاس لجلب الدورات من موقع real.discount"""

    def __init__(self):
        self.base_url = "https://cdn.real.discount/api/courses"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'ar,en-US;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://real.discount/',
            'Origin': 'https://real.discount'
        })
        self.timeout = 30
        self.courses_file = 'real_discount_courses.json'

    def fetch_courses_from_api(self, page=1, limit=14, sort_by='sale_start', language='Arabic'):
        """جلب الدورات العربية من API موقع real.discount"""
        try:
            params = {
                'page': page,
                'limit': limit,
                'sortBy': sort_by,
                'language': 'Arabic'  # التركيز على الدورات العربية فقط
            }

            logger.info(f"جلب الدورات العربية من الصفحة {page}")

            response = self.session.get(
                self.base_url,
                params=params,
                timeout=self.timeout
            )
            response.raise_for_status()

            # طباعة تفاصيل الاستجابة للتشخيص
            logger.info(f"رمز الاستجابة: {response.status_code}")
            logger.info(f"حجم الاستجابة: {len(response.text)} حرف")

            data = response.json()
            logger.info(f"مفاتيح الاستجابة: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            # التحقق من هيكل البيانات
            if isinstance(data, dict) and 'items' in data:
                courses = data['items']
                total_pages = data.get('totalPages', 1)
                logger.info(f"تم العثور على {len(courses)} دورة في الصفحة {page}")
                return courses, total_pages
            elif isinstance(data, dict) and 'courses' in data:
                courses = data['courses']
                logger.info(f"تم العثور على {len(courses)} دورة في الصفحة {page}")
                return courses, data.get('totalPages', 1)
            elif isinstance(data, list):
                # إذا كانت الاستجابة قائمة مباشرة
                logger.info(f"تم العثور على {len(data)} دورة في الصفحة {page} (قائمة مباشرة)")
                return data, 1
            else:
                logger.warning(f"لا توجد دورات في الصفحة {page} - هيكل غير متوقع")
                logger.info(f"عينة من الاستجابة: {str(data)[:200]}...")
                return [], 1

        except requests.exceptions.RequestException as e:
            logger.error(f"خطأ في الطلب للصفحة {page}: {e}")
            return [], 1
        except json.JSONDecodeError as e:
            logger.error(f"خطأ في تحليل JSON للصفحة {page}: {e}")
            return [], 1
        except Exception as e:
            logger.error(f"خطأ غير متوقع في الصفحة {page}: {e}")
            return [], 1

    def process_course_data(self, course):
        """استخراج البيانات الأساسية: الصورة، اسم الدورة، ورابط الكوبون"""
        try:
            # استخراج البيانات الأساسية من هيكل real.discount
            title = course.get('name', course.get('title', 'عنوان غير متوفر'))

            # صورة الدورة
            thumbnail = course.get('image', '')
            if not thumbnail:
                thumbnail = course.get('thumbnail', '')
            if not thumbnail:
                thumbnail = course.get('imageUrl', '')
            if not thumbnail:
                thumbnail = 'https://via.placeholder.com/300x200'

            # رابط الدورة والكوبون
            course_url = course.get('url', course.get('link', ''))
            coupon_code = course.get('couponCode', course.get('coupon', ''))

            # إنشاء رابط Udemy مع الكوبون
            udemy_link = course_url
            if coupon_code and course_url:
                if 'udemy.com' in course_url:
                    separator = '&' if '?' in course_url else '?'
                    udemy_link = f"{course_url}{separator}couponCode={coupon_code}"

            # معلومات السعر
            original_price = course.get('originalPrice', course.get('price', 0))
            current_price = course.get('currentPrice', course.get('salePrice', 0))

            # إذا لم نجد الأسعار، نحاول استخراجها من هيكل مختلف
            if not original_price and 'price' in course:
                price_info = course['price']
                if isinstance(price_info, dict):
                    original_price = price_info.get('original', price_info.get('value', 0))
                    current_price = price_info.get('current', price_info.get('sale', 0))

            # تحديد حالة الدورة
            status = 'paid_with_coupon' if coupon_code else 'paid'
            if current_price == 0:
                status = 'free'

            # إنشاء كائن الدورة المبسط
            processed_course = {
                'title': title,
                'thumbnail': thumbnail,
                'link': udemy_link,
                'coupon_code': coupon_code,
                'original_price': f"{original_price}$" if original_price > 0 else "مجاني",
                'current_price': f"{current_price}$" if current_price > 0 else "مجاني",
                'price_value': current_price,
                'language': 'ar',  # جميع الدورات عربية
                'status': status,
                'coupons_left': 100,  # قيمة افتراضية
                'timestamp': datetime.now().isoformat(),
                'source': 'real.discount'
            }

            # طباعة تفاصيل الدورة للتشخيص
            logger.info(f"معالجة الدورة: {title[:50]}...")
            logger.info(f"الصورة: {thumbnail[:50]}...")
            logger.info(f"الرابط: {udemy_link[:50]}...")
            logger.info(f"الكوبون: {coupon_code}")

            # التحقق من وجود البيانات الأساسية
            if not title:
                logger.warning(f"دورة بدون عنوان")
                return None

            return processed_course

        except Exception as e:
            logger.error(f"خطأ في معالجة بيانات الدورة: {e}")
            logger.error(f"بيانات الدورة: {course}")
            return None

    def has_actual_price(self, course):
        """التحقق من أن الدورة لها سعر حقيقي (ليس مجاني)"""
        try:
            # التحقق من السعر الرقمي
            price_value = course.get('price_value', 0)
            original_price_str = course.get('original_price', '')

            # إذا كان السعر الرقمي أكبر من 0
            if price_value > 0:
                return True

            # التحقق من السعر النصي - استبعاد "مجاني"
            if original_price_str and original_price_str != "مجاني":
                # البحث عن أرقام في النص
                import re
                price_numbers = re.findall(r'\d+\.?\d*', original_price_str)
                if price_numbers:
                    try:
                        price_num = float(price_numbers[0])
                        if price_num > 0:
                            logger.info(f"دورة مدفوعة: {course.get('title', '')[:50]}... - السعر: {original_price_str}")
                            return True
                    except ValueError:
                        pass

            logger.debug(f"دورة مجانية مستبعدة: {course.get('title', '')[:50]}... - السعر: {original_price_str}")
            return False

        except Exception as e:
            logger.error(f"خطأ في فحص السعر: {e}")
            return False

    def fetch_all_courses(self):
        """جلب الدورات من الصفحة الأولى فقط"""
        all_courses = []

        try:
            logger.info("جلب الدورات من الصفحة الأولى فقط")

            # جلب الصفحة الأولى فقط
            courses, total_pages = self.fetch_courses_from_api(page=1, language='Arabic')

            if not courses:
                logger.warning("لم يتم العثور على دورات في الصفحة الأولى")
                return []

            logger.info(f"تم العثور على {len(courses)} دورة في الصفحة الأولى (من أصل {total_pages} صفحة متاحة)")

            # معالجة دورات الصفحة الأولى فقط مع تصفية الدورات المدفوعة
            for course in courses:
                processed_course = self.process_course_data(course)
                if processed_course and self.has_actual_price(processed_course):
                    all_courses.append(processed_course)

            # عكس ترتيب الدورات لإظهار الأحدث أولاً
            all_courses.reverse()
            
            logger.info(f"تم معالجة {len(all_courses)} دورة مدفوعة بنجاح من الصفحة الأولى")
            return all_courses

        except Exception as e:
            logger.error(f"خطأ في جلب الدورات: {e}")
            return []

    def save_courses_to_file(self, courses):
        """حفظ الدورات في ملف JSON"""
        try:
            with open(self.courses_file, 'w', encoding='utf-8') as f:
                json.dump(courses, f, ensure_ascii=False, indent=2)

            logger.info(f"تم حفظ {len(courses)} دورة في ملف {self.courses_file}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ الدورات: {e}")
            return False

    def load_courses_from_file(self):
        """تحميل الدورات من الملف"""
        try:
            if os.path.exists(self.courses_file):
                with open(self.courses_file, 'r', encoding='utf-8') as f:
                    courses = json.load(f)
                logger.info(f"تم تحميل {len(courses)} دورة من ملف {self.courses_file}")
                return courses
            else:
                logger.info(f"ملف {self.courses_file} غير موجود")
                return []

        except Exception as e:
            logger.error(f"خطأ في تحميل الدورات من الملف: {e}")
            return []

    def run(self):
        """تشغيل عملية جلب الدورات من الصفحة الأولى فقط"""
        try:
            logger.info("جلب الدورات من الصفحة الأولى فقط")
            all_courses = self.fetch_all_courses()

            # إزالة الدورات المكررة بناءً على الرابط
            unique_courses = []
            seen_links = set()

            for course in all_courses:
                link = course.get('link', '')
                if link and link not in seen_links:
                    unique_courses.append(course)
                    seen_links.add(link)

            logger.info(f"تم العثور على {len(unique_courses)} دورة عربية فريدة من أصل {len(all_courses)}")

            # حفظ الدورات
            if unique_courses:
                success = self.save_courses_to_file(unique_courses)
                if success:
                    logger.info("تم حفظ الدورات بنجاح")
                    return unique_courses
                else:
                    logger.error("فشل في حفظ الدورات")
                    return []
            else:
                logger.warning("لم يتم العثور على دورات للحفظ")
                return []

        except Exception as e:
            logger.error(f"خطأ في تشغيل عملية جلب الدورات: {e}")
            return []

def main():
    """الدالة الرئيسية لتشغيل السكريبت"""
    print("🚀 جلب الدورات المدفوعة من الصفحة الأولى فقط")
    print("📡 API Endpoint: https://cdn.real.discount/api/courses?page=1&limit=14&sortBy=sale_start&language=Arabic")
    print("🎯 التركيز على: الصور، أسماء الدورات، وروابط الكوبونات")
    print("📄 الصفحة: الأولى فقط")
    print("💰 التصفية: الدورات المدفوعة فقط (استبعاد المجانية)\n")

    scraper = RealDiscountScraper()

    # جلب الدورات من الصفحة الأولى فقط
    courses = scraper.run()

    if courses:
        print(f"✅ تم جلب {len(courses)} دورة بنجاح!")

        # عرض إحصائيات الدورات المدفوعة
        with_coupons = len([c for c in courses if c.get('coupon_code')])
        price_ranges = {
            '1-20$': len([c for c in courses if 1 <= c.get('price_value', 0) <= 20]),
            '21-50$': len([c for c in courses if 21 <= c.get('price_value', 0) <= 50]),
            '50+$': len([c for c in courses if c.get('price_value', 0) > 50])
        }

        print(f"\n📊 إحصائيات الدورات المدفوعة:")
        print(f"   💰 إجمالي الدورات المدفوعة: {len(courses)}")
        print(f"   🎫 دورات بكوبونات: {with_coupons}")
        print(f"   💵 فئات الأسعار:")
        for price_range, count in price_ranges.items():
            if count > 0:
                print(f"     - {price_range}: {count} دورة")

        # عرض عينة من الدورات مع التركيز على البيانات الأساسية
        print(f"\n📚 عينة من الدورات العربية:")
        for i, course in enumerate(courses[:3]):
            print(f"\n   {i+1}. 📖 العنوان: {course.get('title', 'N/A')}")
            print(f"      🖼️  الصورة: {course.get('thumbnail', 'N/A')}")
            print(f"      🔗 رابط الكوبون: {course.get('link', 'N/A')}")
            if course.get('coupon_code'):
                print(f"      🎫 كود الكوبون: {course.get('coupon_code')}")
            print(f"      💰 السعر: {course.get('original_price', 'N/A')} → {course.get('current_price', 'N/A')}")
            print(f"      🌐 اللغة: 🇸🇦 عربي")
            print(f"      📅 المصدر: real.discount")
    else:
        print("❌ فشل في جلب الدورات المدفوعة من real.discount")

if __name__ == "__main__":
    main()
