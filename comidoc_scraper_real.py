#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت جلب دورات Comidoc الحقيقي
يجلب الدورات المجانية من موقع Comidoc باستخدام نفس الكود من البوت
"""

import requests
import json
from datetime import datetime, timezone
import time
import re
from bs4 import BeautifulSoup
import bleach
from functools import lru_cache
import logging
import asyncio

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الإعدادات
class Config:
    BASE_URL = 'https://comidoc.net'
    REQUEST_TIMEOUT = 10
    COURSES_FILE = 'courses.json'
    MAX_PAGES = 10  # عدد الصفحات للمعالجة
    CONCURRENT_REQUESTS = 5  # عدد الطلبات المتزامنة
    CACHE_TIMEOUT = 3600  # مدة صلاحية التخزين المؤقت بالثواني

# إعداد جلسة الطلبات
session = requests.Session()
session.headers.update({'User-Agent': 'Mozilla/5.0 (compatible; CourseBot/1.0)'})

# نظام التخزين المؤقت
cache = {}

def get_cached_data(key, fetch_func, timeout=Config.CACHE_TIMEOUT):
    """الحصول على البيانات من التخزين المؤقت أو جلبها إذا لم تكن موجودة"""
    current_time = time.time()
    if key in cache and current_time - cache[key]['timestamp'] < timeout:
        return cache[key]['data']

    # جلب البيانات
    data = fetch_func()

    # تخزين البيانات في التخزين المؤقت
    cache[key] = {
        'data': data,
        'timestamp': current_time
    }

    return data

# تخزين مؤقت للطلبات HTTP
@lru_cache(maxsize=100)
def cached_request(url, timeout=Config.REQUEST_TIMEOUT):
    """إجراء طلب HTTP مع تخزين مؤقت"""
    response = session.get(url, timeout=timeout)
    response.raise_for_status()
    return response.text

def check_course_coupon(course_url):
    """التحقق من حالة الكوبون في صفحة الدورة"""
    try:
        # التحقق من صحة الرابط
        if not course_url.startswith(('http://', 'https://')):
            return "error"

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)

        # تحليل HTML بشكل أكثر كفاءة
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن جميع النصوص المتعلقة بالكوبونات في مرة واحدة
        all_text = ' '.join([element.get_text() for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6')])
        all_text_upper = all_text.upper()

        # التحقق من الكوبون المجاني
        if '100%OFF COUPON' in all_text_upper or '100% OFF' in all_text_upper:
            return "paid_with_coupon"

        # التحقق من الكوبون المخفض
        if 'COUPON' in all_text_upper and '$' in all_text and not ('100%' in all_text_upper and 'OFF' in all_text_upper):
            return "discounted"

        # التحقق من الخصومات الجزئية
        if re.search(r'\d+%\s*OFF', all_text_upper) and not re.search(r'100%\s*OFF', all_text_upper):
            return "discounted"

        # إذا لم يتم العثور على أي خصم، فالكوبون منتهي الصلاحية
        return "expired"

    except requests.exceptions.RequestException as e:
        logger.error(f"خطأ في الاتصال: {e}")
        return "error"
    except Exception as e:
        logger.error(f"خطأ غير متوقع: {e}")
        return "error"

def extract_udemy_link_and_coupons_left(course_url):
    """استخراج رابط Udemy مع كود الخصم وعدد الكوبونات المتبقية والسعر الأصلي من صفحة الدورة"""
    try:
        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(course_url)
        soup = BeautifulSoup(html_content, 'html.parser')

        # البحث عن رابط Udemy بشكل أكثر كفاءة
        udemy_link = soup.select_one('a[href*="udemy.com/course"][href*="couponCode"]')

        # البحث عن عدد الكوبونات المتبقية بطريقة أكثر كفاءة
        coupons_left = 0

        # جمع كل النصوص التي تحتوي على "left" أو "remaining"
        left_texts = []
        for element in soup.select('div, span, p, strong'):
            text = element.get_text().lower().strip()
            if 'left' in text or 'remaining' in text:
                left_texts.append(text)

        # البحث عن نمط الكوبونات المتبقية في النصوص المجمعة
        for text in left_texts:
            # تحديد النص الذي يحتوي على "left" وأرقام قبله
            match = re.search(r'(\d+)\s*(?:coupons?|codes?)?(?:\s+left|\s+remaining)', text)
            if match:
                coupons_left = int(match.group(1))
                # التحقق من أن الرقم منطقي (أقل من 10000)
                if coupons_left > 0 and coupons_left < 10000:
                    break

        # إذا لم نجد عدد الكوبونات، نبحث عن أي رقم قريب من كلمة "left"
        if coupons_left == 0:
            for text in left_texts:
                # استخراج جميع الأرقام من النص
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:  # نتأكد أن الرقم ليس كبيرًا جدًا
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # البحث عن نص عربي يحتوي على "متبقي" أو "متبقية"
        if coupons_left == 0:
            arabic_texts = []
            for element in soup.select('div, span, p, strong'):
                text = element.get_text().strip()
                if 'متبقي' in text or 'متبقية' in text:
                    arabic_texts.append(text)

            for text in arabic_texts:
                # استخراج الأرقام من النص العربي
                numbers = re.findall(r'\d+', text)
                for num in numbers:
                    if len(num) < 5:
                        coupons_left = int(num)
                        if coupons_left > 0:
                            break
                if coupons_left > 0:
                    break

        # إذا لم نجد عدد الكوبونات، نفترض أن هناك 100 كوبون متبقي
        if coupons_left == 0 or coupons_left > 10000:
            coupons_left = 100

        # استخراج صورة الدورة في نفس الوظيفة
        thumbnail = 'https://via.placeholder.com/150'  # صورة افتراضية
        picture = soup.find('picture')

        if picture:
            img = picture.find('img')
            if img and img.get('src'):
                thumbnail = img['src']
            else:
                source = picture.find('source', type='image/jpeg')
                if source and source.get('srcset'):
                    thumbnail = source['srcset']

        # البحث عن صورة في أماكن أخرى إذا لم نجدها
        if thumbnail == 'https://via.placeholder.com/150':
            img = soup.select_one('img.course-image, img.course-thumbnail, img[alt*="course"], img[alt*="Course"]')
            if img and img.get('src'):
                thumbnail = img['src']

        # استخراج السعر الأصلي للدورة
        original_price = "49.99$"  # سعر افتراضي

        # البحث عن السعر في صفحة الدورة
        price_elements = soup.select('.text-2xl.font-medium.text-white, .text-xl.font-medium.text-white, .text-lg.font-medium.text-white, div[class*="price"], span[class*="price"]')
        for element in price_elements:
            text = element.get_text().strip()
            # البحث عن نمط السعر (مثل $13.99 أو 13.99$)
            price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
            if price_match:
                original_price = price_match.group(1)
                break

        # إذا لم نجد السعر في العناصر المحددة، نبحث في كل النص
        if original_price == "49.99$":
            for element in soup.select('div, span, p, h1, h2, h3, h4, h5, h6'):
                text = element.get_text().strip()
                price_match = re.search(r'(\$\d+\.\d+|\d+\.\d+\$|\$\d+|\d+\$)', text)
                if price_match:
                    original_price = price_match.group(1)
                    # التأكد من أن السعر منطقي (أكبر من 5 دولارات)
                    price_value = float(re.sub(r'[^\d.]', '', original_price))
                    if price_value > 5:
                        break

        return udemy_link['href'] if udemy_link else None, coupons_left, thumbnail, original_price

    except Exception as e:
        logger.error(f"خطأ في استخراج رابط Udemy: {e}")
        return None, 0, 'https://via.placeholder.com/150', "49.99$"

def process_page(page, language='ar'):
    """معالجة صفحة واحدة من الدورات"""
    courses = []
    try:
        url = f'{Config.BASE_URL}/coupons?page={page}'

        # استخدام التخزين المؤقت للطلبات
        html_content = cached_request(url)
        soup = BeautifulSoup(html_content, 'html.parser')

        flag_icon = 'ar.svg' if language == 'ar' else 'us.svg'

        # استخدام محدد CSS أكثر دقة
        course_divs = soup.select('div.relative')

        # تسجيل عدد الدورات التي تم العثور عليها
        logger.info(f"تم العثور على {len(course_divs)} دورة في الصفحة {page}")

        for course_div in course_divs:
            # التحقق من لغة الدورة
            if course_div.find('img', src=lambda x: x and flag_icon in x):
                title_elem = course_div.find('h2')
                link_elem = course_div.find('a', href=lambda x: x and '/udemy/' in x)

                if title_elem and link_elem:
                    title = bleach.clean(title_elem.get_text(strip=True))
                    full_link = f"{Config.BASE_URL}{link_elem['href']}"

                    try:
                        # التحقق من حالة الكوبون
                        coupon_status = check_course_coupon(full_link)

                        # فقط متابعة المعالجة إذا كان الكوبون مجاني بالكامل
                        if coupon_status == "paid_with_coupon":
                            # استخراج رابط Udemy وعدد الكوبونات المتبقية والصورة والسعر الأصلي في طلب واحد
                            udemy_link, coupons_left, thumbnail, original_price = extract_udemy_link_and_coupons_left(full_link)

                            # فقط إضافة الدورات التي لديها كوبونات متبقية ورابط Udemy صالح
                            if udemy_link and coupons_left > 0:
                                courses.append({
                                    'title': title,
                                    'link': udemy_link,
                                    'comidoc_link': full_link,
                                    'thumbnail': thumbnail,
                                    'coupons_left': coupons_left,
                                    'original_price': original_price,
                                    'language': language,
                                    'timestamp': datetime.now(timezone.utc).isoformat(),
                                    'status': coupon_status
                                })
                                logger.info(f"تمت إضافة دورة مجانية: {title}")
                        elif coupon_status == "discounted":
                            logger.debug(f"تم تخطي دورة بخصم جزئي: {title}")
                        elif coupon_status == "expired":
                            logger.debug(f"تم تخطي دورة منتهية الصلاحية: {title}")

                    except Exception as e:
                        logger.error(f"خطأ في جلب تفاصيل الدورة {full_link}: {e}")
                        continue

        return courses

    except Exception as e:
        logger.error(f"خطأ في معالجة الصفحة {page}: {e}")
        return []

def fetch_all_courses(language='ar'):
    """جلب جميع الدورات من عدة صفحات"""
    # استخدام عدد الصفحات المحدد في الإعدادات
    total_pages = Config.MAX_PAGES
    all_courses = []

    # تسجيل بداية عملية جلب الدورات
    start_time = time.time()
    logger.info(f"بدء جلب الدورات من {total_pages} صفحة...")

    # معالجة الصفحات بشكل متتالي (بدون asyncio للبساطة)
    for page in range(1, total_pages + 1):
        logger.info(f"معالجة الصفحة {page}...")
        try:
            courses = process_page(page, language)
            all_courses.extend(courses)
            logger.info(f"تم الانتهاء من معالجة الصفحة {page} - تم العثور على {len(courses)} دورة")
            
            # توقف قصير بين الصفحات لتجنب الحمل الزائد
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الصفحة {page}: {e}")
            continue

    # حفظ الدورات في الملف
    try:
        with open(Config.COURSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_courses, f, ensure_ascii=False, indent=2)

        # حساب الوقت المستغرق
        elapsed_time = time.time() - start_time
        logger.info(f"تم حفظ {len(all_courses)} دورة بنجاح في {elapsed_time:.2f} ثانية")
    except Exception as e:
        logger.error(f"خطأ في حفظ الدورات: {e}")

    return all_courses

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل سكريبت Comidoc الحقيقي...")
    print("=" * 50)
    
    try:
        # جلب الدورات العربية
        courses = fetch_all_courses('ar')
        
        if courses:
            print("=" * 50)
            print(f"✅ تم الانتهاء بنجاح! تم جلب {len(courses)} دورة من Comidoc")
            print("📁 الملف المحفوظ: courses.json")
            
            # عرض عينة من الدورات
            print("\n📚 عينة من الدورات المجلبة:")
            for i, course in enumerate(courses[:3]):
                print(f"{i+1}. {course['title']}")
                print(f"   💰 السعر: {course['original_price']}")
                print(f"   👥 الكوبونات المتبقية: {course['coupons_left']}")
                print()
        else:
            print("❌ لم يتم جلب أي دورات")
            
    except Exception as e:
        logger.error(f"خطأ في التشغيل: {e}")
        print(f"❌ حدث خطأ: {e}")

if __name__ == "__main__":
    main()
